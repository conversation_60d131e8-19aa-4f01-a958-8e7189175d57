import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { ApiResponse } from "@/types";

// Create axios instance with default config
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:3001/api/v1",
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || "10000"),
  withCredentials: true, // Enable sending cookies and credentials
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and token refresh
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Check for new token in response headers
    const newToken = response.headers["x-new-access-token"];
    if (newToken) {
      localStorage.setItem("accessToken", newToken);
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem("refreshToken");
        if (refreshToken) {
          const response = await axios.post(
            `${api.defaults.baseURL}/auth/refresh`,
            { refreshToken }
          );

          const { accessToken } = response.data.data;
          localStorage.setItem("accessToken", accessToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        window.location.href = "/login";
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API helper functions
export const apiClient = {
  // Generic request method
  async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await api(config);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  },

  // GET request
  async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "GET", url });
  },

  // POST request
  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "POST", url, data });
  },

  // PUT request
  async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "PUT", url, data });
  },

  // DELETE request
  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "DELETE", url });
  },

  // PATCH request
  async patch<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "PATCH", url, data });
  },

  // Error handler
  handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message =
        error.response.data?.error ||
        error.response.data?.message ||
        "An error occurred";
      const statusCode = error.response.status;

      const apiError = new Error(message) as any;
      apiError.statusCode = statusCode;
      apiError.details = error.response.data?.details;

      return apiError;
    } else if (error.request) {
      // Request was made but no response received
      return new Error("Network error - please check your connection");
    } else {
      // Something else happened
      return new Error(error.message || "An unexpected error occurred");
    }
  },

  // File upload helper
  async uploadFile<T>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append("file", file);

    return this.request<T>({
      method: "POST",
      url,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    });
  },
};

// Auth API
export const authApi = {
  async login(credentials: { email: string; password: string }) {
    return apiClient.post("/auth/login", credentials);
  },

  async register(userData: {
    email: string;
    username: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) {
    return apiClient.post("/auth/register", userData);
  },

  async logout() {
    return apiClient.post("/auth/logout");
  },

  async refreshToken(refreshToken: string) {
    return apiClient.post("/auth/refresh", { refreshToken });
  },

  async getProfile() {
    return apiClient.get("/auth/profile");
  },

  async updateProfile(updates: any) {
    return apiClient.put("/auth/profile", updates);
  },

  async changePassword(data: { currentPassword: string; newPassword: string }) {
    return apiClient.post("/auth/change-password", data);
  },

  async requestPasswordReset(email: string) {
    return apiClient.post("/auth/request-password-reset", { email });
  },

  async verifyEmail(token: string) {
    return apiClient.post(`/auth/verify-email/${token}`);
  },

  async deleteAccount(password: string) {
    return apiClient.delete("/auth/account", { data: { password } });
  },
};

// Deck API
export const deckApi = {
  async getDecks(params?: any) {
    return apiClient.get("/decks", { params });
  },

  async getPublicDecks(params?: any) {
    return apiClient.get("/decks/public", { params });
  },

  async getDeck(id: string) {
    return apiClient.get(`/decks/${id}`);
  },

  async createDeck(data: any) {
    return apiClient.post("/decks", data);
  },

  async updateDeck(id: string, data: any) {
    return apiClient.put(`/decks/${id}`, data);
  },

  async deleteDeck(id: string) {
    return apiClient.delete(`/decks/${id}`);
  },

  async getDeckStats(id: string) {
    return apiClient.get(`/decks/${id}/stats`);
  },

  async duplicateDeck(id: string, title?: string) {
    return apiClient.post(`/decks/${id}/duplicate`, { title });
  },

  async shareDeck(id: string) {
    return apiClient.post(`/decks/${id}/share`);
  },

  async unshareDeck(id: string) {
    return apiClient.post(`/decks/${id}/unshare`);
  },

  async getCategories() {
    return apiClient.get("/decks/categories");
  },

  async searchDecks(query: string, type?: string) {
    return apiClient.get("/decks/search", { params: { q: query, type } });
  },
};

// Card API
export const cardApi = {
  async getCards(deckId: string, params?: any) {
    return apiClient.get(`/cards/deck/${deckId}`, { params });
  },

  async getCard(id: string) {
    return apiClient.get(`/cards/${id}`);
  },

  async createCard(deckId: string, data: any) {
    return apiClient.post(`/cards/deck/${deckId}`, data);
  },

  async updateCard(id: string, data: any) {
    return apiClient.put(`/cards/${id}`, data);
  },

  async deleteCard(id: string) {
    return apiClient.delete(`/cards/${id}`);
  },

  async bulkCreateCards(deckId: string, cards: any[]) {
    return apiClient.post(`/cards/deck/${deckId}/bulk`, { cards });
  },

  async reorderCards(deckId: string, cardIds: string[]) {
    return apiClient.put(`/cards/deck/${deckId}/reorder`, { cardIds });
  },

  async duplicateCard(id: string) {
    return apiClient.post(`/cards/${id}/duplicate`);
  },

  async getCardTypes() {
    return apiClient.get("/cards/types");
  },

  async exportCards(deckId: string, format: string = "json") {
    return apiClient.get(`/cards/deck/${deckId}/export`, {
      params: { format },
    });
  },
};

// Study API
export const studyApi = {
  async getDueCards(deckId?: string, params?: any) {
    const url = deckId ? `/study/due?deckId=${deckId}` : "/study/due";
    return apiClient.get(url, { params });
  },

  async getNewCards(deckId?: string, params?: any) {
    const url = deckId ? `/study/new?deckId=${deckId}` : "/study/new";
    return apiClient.get(url, { params });
  },

  async createSession(data: any) {
    return apiClient.post("/study/sessions", data);
  },

  async endSession(sessionId: string) {
    return apiClient.put(`/study/sessions/${sessionId}/end`);
  },

  async submitReview(data: any) {
    return apiClient.post("/study/reviews", data);
  },

  async getCardReview(cardId: string) {
    return apiClient.get(`/study/reviews/${cardId}`);
  },

  async getStats() {
    return apiClient.get("/study/stats");
  },

  async getRecommendations() {
    return apiClient.get("/study/recommendations");
  },

  async getStudyCalendar(year?: number) {
    return apiClient.get("/study/calendar", { params: { year } });
  },

  async getInsights(period?: string) {
    return apiClient.get("/study/insights", { params: { period } });
  },

  async getOptimalStudyTime() {
    return apiClient.get("/study/optimal-time");
  },
};

export default api;
