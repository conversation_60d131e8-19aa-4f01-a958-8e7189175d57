import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import {
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  CheckCircle,
  XCircle,
  Zap,
} from "lucide-react";

interface StudyControlsProps {
  isCardFlipped: boolean;
  onAnswer: (quality: number) => void;
  onFlipCard: () => void;
  onSkip?: () => void;
  disabled?: boolean;
  className?: string;
}

export function StudyControls({
  isCardFlipped,
  onAnswer,
  onFlipCard,
  onSkip,
  disabled = false,
  className,
}: StudyControlsProps) {
  const difficultyButtons = [
    {
      quality: 1,
      label: "Again",
      icon: XCircle,
      color: "bg-red-500 hover:bg-red-600 text-white",
      description: "Incorrect - show again soon",
    },
    {
      quality: 2,
      label: "Hard",
      icon: ThumbsDown,
      color: "bg-orange-500 hover:bg-orange-600 text-white",
      description: "Correct but difficult",
    },
    {
      quality: 3,
      label: "Good",
      icon: CheckCircle,
      color: "bg-green-500 hover:bg-green-600 text-white",
      description: "Correct with some effort",
    },
    {
      quality: 4,
      label: "Easy",
      icon: ThumbsUp,
      color: "bg-blue-500 hover:bg-blue-600 text-white",
      description: "Correct and easy",
    },
    {
      quality: 5,
      label: "Perfect",
      icon: Zap,
      color: "bg-purple-500 hover:bg-purple-600 text-white",
      description: "Perfect recall",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { type: "spring", stiffness: 300 },
    },
  };

  if (!isCardFlipped) {
    return (
      <motion.div
        className={cn("flex justify-center space-x-4", className)}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={buttonVariants}>
          <Button
            onClick={onFlipCard}
            disabled={disabled}
            size="lg"
            className="px-8 py-3 text-lg font-medium"
            leftIcon={<RotateCcw className="w-5 h-5" />}
          >
            Show Answer
          </Button>
        </motion.div>

        {onSkip && (
          <motion.div variants={buttonVariants}>
            <Button
              onClick={onSkip}
              disabled={disabled}
              variant="outline"
              size="lg"
              className="px-6 py-3"
            >
              Skip
            </Button>
          </motion.div>
        )}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={cn("space-y-6", className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header Section */}
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          How well did you know this?
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          Your answer affects when you'll see this card again
        </p>
      </div>

      {/* Difficulty Buttons - Responsive Grid Layout */}
      <div className="w-full max-w-4xl mx-auto">
        {/* Mobile Layout: 2x3 Grid */}
        <div className="grid grid-cols-2 gap-3 sm:hidden">
          {difficultyButtons.map((button) => {
            const Icon = button.icon;
            return (
              <motion.div key={button.quality} variants={buttonVariants}>
                <Button
                  onClick={() => onAnswer(button.quality)}
                  disabled={disabled}
                  className={cn(
                    "flex flex-col items-center justify-center p-4 h-20 w-full space-y-1 transition-all duration-200 shadow-md hover:shadow-lg",
                    button.color,
                    "border-0 focus:ring-2 focus:ring-offset-2 focus:ring-white/20"
                  )}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium text-sm">{button.label}</span>
                </Button>
              </motion.div>
            );
          })}
        </div>

        {/* Tablet Layout: Single Row with Smaller Buttons */}
        <div className="hidden sm:flex lg:hidden justify-center gap-2">
          {difficultyButtons.map((button) => {
            const Icon = button.icon;
            return (
              <motion.div key={button.quality} variants={buttonVariants}>
                <Button
                  onClick={() => onAnswer(button.quality)}
                  disabled={disabled}
                  className={cn(
                    "flex flex-col items-center justify-center p-3 h-auto min-w-[90px] space-y-1 transition-all duration-200 shadow-md hover:shadow-lg",
                    button.color,
                    "border-0 focus:ring-2 focus:ring-offset-2 focus:ring-white/20"
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium text-sm">{button.label}</span>
                  <span className="text-xs opacity-90 text-center leading-tight">
                    {button.description.split(" - ")[0]}
                  </span>
                </Button>
              </motion.div>
            );
          })}
        </div>

        {/* Desktop Layout: Single Row with Full Descriptions */}
        <div className="hidden lg:flex justify-center gap-4">
          {difficultyButtons.map((button) => {
            const Icon = button.icon;
            return (
              <motion.div key={button.quality} variants={buttonVariants}>
                <Button
                  onClick={() => onAnswer(button.quality)}
                  disabled={disabled}
                  className={cn(
                    "flex flex-col items-center justify-center p-4 h-auto min-w-[120px] space-y-2 transition-all duration-200 shadow-md hover:shadow-lg",
                    button.color,
                    "border-0 focus:ring-2 focus:ring-offset-2 focus:ring-white/20"
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className="w-6 h-6" />
                  <span className="font-semibold text-base">
                    {button.label}
                  </span>
                  <span className="text-xs opacity-90 text-center leading-tight">
                    {button.description}
                  </span>
                </Button>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Flip Back Button */}
      <motion.div
        className="flex justify-center pt-2"
        variants={buttonVariants}
      >
        <Button
          onClick={onFlipCard}
          disabled={disabled}
          variant="ghost"
          size="sm"
          className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
          leftIcon={<RotateCcw className="w-4 h-4" />}
        >
          Flip Back
        </Button>
      </motion.div>
    </motion.div>
  );
}
