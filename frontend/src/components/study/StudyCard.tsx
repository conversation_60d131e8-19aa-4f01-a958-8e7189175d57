import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/Card';

interface StudyCardProps {
  frontContent: any;
  backContent: any;
  isFlipped: boolean;
  onFlip: () => void;
  className?: string;
}

export function StudyCard({
  frontContent,
  backContent,
  isFlipped,
  onFlip,
  className
}: StudyCardProps) {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleFlip = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    onFlip();
    setTimeout(() => setIsAnimating(false), 300);
  };

  const cardVariants = {
    front: {
      rotateY: 0,
      transition: { duration: 0.3, ease: "easeInOut" }
    },
    back: {
      rotateY: 180,
      transition: { duration: 0.3, ease: "easeInOut" }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.2, delay: 0.1 }
    }
  };

  return (
    <div className={cn("relative w-full h-80 perspective-1000", className)}>
      <motion.div
        className="relative w-full h-full cursor-pointer"
        style={{ transformStyle: "preserve-3d" }}
        variants={cardVariants}
        animate={isFlipped ? "back" : "front"}
        onClick={handleFlip}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Front of card */}
        <motion.div
          className="absolute inset-0 w-full h-full backface-hidden"
          style={{ backfaceVisibility: "hidden" }}
        >
          <Card className="w-full h-full flex items-center justify-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-2 border-blue-200 dark:border-blue-700">
            <AnimatePresence mode="wait">
              {!isFlipped && (
                <motion.div
                  key="front"
                  variants={contentVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="text-center w-full"
                >
                  <div className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {renderContent(frontContent)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Click to reveal answer
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        </motion.div>

        {/* Back of card */}
        <motion.div
          className="absolute inset-0 w-full h-full backface-hidden"
          style={{ 
            backfaceVisibility: "hidden",
            transform: "rotateY(180deg)"
          }}
        >
          <Card className="w-full h-full flex items-center justify-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-700">
            <AnimatePresence mode="wait">
              {isFlipped && (
                <motion.div
                  key="back"
                  variants={contentVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="text-center w-full"
                >
                  <div className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {renderContent(backContent)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Click to flip back
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  );
}

function renderContent(content: any): React.ReactNode {
  if (typeof content === 'string') {
    return content;
  }
  
  if (content && typeof content === 'object') {
    // Handle JSONB content structure
    if (Array.isArray(content)) {
      return content.map((item, index) => (
        <div key={index} className="mb-2">
          {renderContentItem(item)}
        </div>
      ));
    }
    
    if (content.type === 'text') {
      return content.content;
    }
    
    // Fallback for other content types
    return JSON.stringify(content);
  }
  
  return 'No content available';
}

function renderContentItem(item: any): React.ReactNode {
  if (typeof item === 'string') {
    return item;
  }
  
  if (item && typeof item === 'object') {
    switch (item.type) {
      case 'text':
        return <span>{item.content}</span>;
      case 'image':
        return (
          <img 
            src={item.content} 
            alt={item.alt || 'Card image'} 
            className="max-w-full h-auto rounded-lg"
          />
        );
      default:
        return <span>{item.content || JSON.stringify(item)}</span>;
    }
  }
  
  return item;
}
