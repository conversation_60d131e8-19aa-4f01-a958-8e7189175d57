@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Import Inter font */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap");

/* Base styles */
@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }

  ::-moz-selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-ring disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-800 dark:active:bg-gray-700;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-md {
    @apply px-4 py-2 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Input styles */
  .input {
    @apply block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-400 focus-ring dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-500;
  }

  .input-error {
    @apply border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600;
  }

  /* Card styles */
  .card {
    @apply rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800;
  }

  .card-hover {
    @apply transition-shadow hover:shadow-md;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Glass effect */
  .glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80;
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700;
  }
}

/* Utility classes */
@layer utilities {
  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Text balance */
  .text-balance {
    text-wrap: balance;
  }

  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 0.6s ease-in-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
  }
}

/* Custom CSS variables for consistent theming */
:root {
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 100 116 139;
  --color-secondary-foreground: 255 255 255;
}

.dark {
  --color-primary: 96 165 250;
  --color-primary-foreground: 15 23 42;
  --color-secondary: 71 85 105;
  --color-secondary-foreground: 248 250 252;
}

/* Keyframes for animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounceSubtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 3D Transform utilities */
.perspective-1000 {
  perspective: 1000px;
}

.backface-hidden {
  backface-visibility: hidden;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Flashcard specific styles */
.flashcard {
  @apply relative w-full h-64 perspective-1000;
}

.flashcard-inner {
  @apply relative w-full h-full transition-transform duration-600 transform-style-preserve-3d;
}

.flashcard.flipped .flashcard-inner {
  @apply rotate-y-180;
}

.flashcard-front,
.flashcard-back {
  @apply absolute inset-0 w-full h-full backface-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800;
}

.flashcard-back {
  @apply rotate-y-180;
}

/* Study session progress bar */
.progress-bar {
  @apply relative h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-300 ease-out;
}

/* Difficulty indicators */
.difficulty-1 {
  @apply text-green-600 dark:text-green-400;
}
.difficulty-2 {
  @apply text-yellow-600 dark:text-yellow-400;
}
.difficulty-3 {
  @apply text-orange-600 dark:text-orange-400;
}
.difficulty-4 {
  @apply text-red-600 dark:text-red-400;
}
.difficulty-5 {
  @apply text-purple-600 dark:text-purple-400;
}
