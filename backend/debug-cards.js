const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moflash_dev',
  user: 'moflash_user',
  password: 'moflash_password',
});

async function debugCards() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Debugging card content...');
    
    const result = await client.query(`
      SELECT id, front_content, back_content, 
             pg_typeof(front_content) as front_type,
             pg_typeof(back_content) as back_type
      FROM cards 
      LIMIT 3
    `);
    
    console.log('📋 Card data:');
    result.rows.forEach((row, index) => {
      console.log(`\nCard ${index + 1}:`);
      console.log(`  ID: ${row.id}`);
      console.log(`  Front content type: ${row.front_type}`);
      console.log(`  Front content: ${JSON.stringify(row.front_content)}`);
      console.log(`  Back content type: ${row.back_type}`);
      console.log(`  Back content: ${JSON.stringify(row.back_content)}`);
      console.log(`  Front typeof: ${typeof row.front_content}`);
      console.log(`  Back typeof: ${typeof row.back_content}`);
    });
    
  } catch (error) {
    console.error('❌ Error debugging cards:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

debugCards();
