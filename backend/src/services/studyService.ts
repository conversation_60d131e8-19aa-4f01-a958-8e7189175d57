import { db, db<PERSON><PERSON><PERSON> } from "@/utils/database";
import {
  StudySession,
  CardReview,
  CreateReviewData,
  DueCard,
  StudyStats,
  AppError,
} from "@/types";
import { SpacedRepetitionAlgorithm } from "@/utils/spacedRepetition";
import { deckService } from "./deckService";
import { cardService } from "./cardService";
import { logger } from "@/utils/logger";

export class StudyService {
  /**
   * Create a new study session
   */
  async createSession(
    userId: string,
    deckId: string,
    sessionType: string = "review"
  ): Promise<StudySession> {
    try {
      // Verify deck access
      const deck = await deckService.findById(deckId, userId);
      if (!deck) {
        throw new AppError("Deck not found", 404);
      }

      const query = `
        INSERT INTO study_sessions (user_id, deck_id, session_type)
        VALUES ($1, $2, $3)
        RETURNING id, user_id, deck_id, session_type, started_at, ended_at,
                  cards_studied, correct_answers, total_time_seconds, average_response_time, metadata
      `;

      const values = [userId, deckId, sessionType];
      const result = await db.query(query, values);
      const session = dbHelpers.toCamelCase(result.rows[0]) as StudySession;

      logger.info("Study session created", {
        sessionId: session.id,
        userId,
        deckId,
      });
      return session;
    } catch (error) {
      logger.error("Failed to create study session", {
        error,
        userId,
        deckId,
        sessionType,
      });
      throw error;
    }
  }

  /**
   * End a study session
   */
  async endSession(sessionId: string, userId: string): Promise<StudySession> {
    try {
      // Calculate session statistics
      const statsQuery = `
        SELECT 
          COUNT(*) as cards_studied,
          COUNT(CASE WHEN quality >= 3 THEN 1 END) as correct_answers,
          AVG(response_time_ms) as average_response_time
        FROM card_reviews
        WHERE session_id = $1 AND user_id = $2
      `;

      const statsResult = await db.query(statsQuery, [sessionId, userId]);
      const stats = statsResult.rows[0];

      const query = `
        UPDATE study_sessions
        SET 
          ended_at = NOW(),
          cards_studied = $3,
          correct_answers = $4,
          total_time_seconds = EXTRACT(EPOCH FROM (NOW() - started_at)),
          average_response_time = $5
        WHERE id = $1 AND user_id = $2
        RETURNING id, user_id, deck_id, session_type, started_at, ended_at,
                  cards_studied, correct_answers, total_time_seconds, average_response_time, metadata
      `;

      const values = [
        sessionId,
        userId,
        parseInt(stats.cards_studied) || 0,
        parseInt(stats.correct_answers) || 0,
        parseInt(stats.average_response_time) || 0,
      ];

      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        throw new AppError("Study session not found", 404);
      }

      const session = dbHelpers.toCamelCase(result.rows[0]) as StudySession;
      logger.info("Study session ended", { sessionId, userId });
      return session;
    } catch (error) {
      logger.error("Failed to end study session", { error, sessionId, userId });
      throw error;
    }
  }

  /**
   * Get cards due for review
   */
  async getDueCards(
    userId: string,
    deckId?: string,
    limit: number = 20
  ): Promise<DueCard[]> {
    try {
      let whereClause = `
        WHERE cr.user_id = $1 
        AND cr.next_review_date <= NOW()
      `;
      const values = [userId];
      let paramIndex = 2;

      if (deckId) {
        whereClause += ` AND c.deck_id = $${paramIndex}`;
        values.push(deckId);
        paramIndex++;
      }

      // Only include cards from accessible decks
      whereClause += ` AND (d.user_id = $1 OR d.is_public = true)`;

      const query = `
        SELECT 
          c.id, c.deck_id, c.front_content, c.back_content, c.card_type, 
          c.difficulty, c.tags, c.position, c.created_at, c.updated_at, c.metadata,
          cr.next_review_date, cr.ease_factor, cr.interval_days, cr.repetitions
        FROM card_reviews cr
        JOIN cards c ON cr.card_id = c.id
        JOIN decks d ON c.deck_id = d.id
        ${whereClause}
        ORDER BY cr.next_review_date ASC
        LIMIT $${paramIndex}
      `;

      values.push(limit.toString());
      const result = await db.query(query, values);

      const dueCards = result.rows.map((row: any) => {
        const card = dbHelpers.toCamelCase(row) as any;
        // JSONB fields are already parsed by PostgreSQL
        card.frontContent = row.front_content;
        card.backContent = row.back_content;

        return {
          card: {
            id: card.id,
            deckId: card.deckId,
            frontContent: card.frontContent,
            backContent: card.backContent,
            cardType: card.cardType,
            difficulty: card.difficulty,
            tags: card.tags,
            position: card.position,
            createdAt: card.createdAt,
            updatedAt: card.updatedAt,
            metadata: card.metadata,
          },
          nextReviewDate: card.nextReviewDate,
          easeFactor: card.easeFactor,
          intervalDays: card.intervalDays,
          repetitions: card.repetitions,
        } as DueCard;
      });

      return dueCards;
    } catch (error) {
      logger.error("Failed to get due cards", { error, userId, deckId, limit });
      throw error;
    }
  }

  /**
   * Get new cards for learning
   */
  async getNewCards(
    userId: string,
    deckId?: string,
    limit: number = 10
  ): Promise<any[]> {
    try {
      let whereClause = `
        WHERE NOT EXISTS (
          SELECT 1 FROM card_reviews cr 
          WHERE cr.card_id = c.id AND cr.user_id = $1
        )
      `;
      const values = [userId];
      let paramIndex = 2;

      if (deckId) {
        whereClause += ` AND c.deck_id = $${paramIndex}`;
        values.push(deckId);
        paramIndex++;
      }

      // Only include cards from accessible decks
      whereClause += ` AND (d.user_id = $1 OR d.is_public = true)`;

      const query = `
        SELECT 
          c.id, c.deck_id, c.front_content, c.back_content, c.card_type, 
          c.difficulty, c.tags, c.position, c.created_at, c.updated_at, c.metadata
        FROM cards c
        JOIN decks d ON c.deck_id = d.id
        ${whereClause}
        ORDER BY c.position ASC
        LIMIT $${paramIndex}
      `;

      values.push(limit.toString());
      const result = await db.query(query, values);

      const newCards = result.rows.map((row: any) => {
        const card = dbHelpers.toCamelCase(row) as any;
        // JSONB fields are already parsed by PostgreSQL
        card.frontContent = row.front_content;
        card.backContent = row.back_content;
        return card;
      });

      return newCards;
    } catch (error) {
      logger.error("Failed to get new cards", { error, userId, deckId, limit });
      throw error;
    }
  }

  /**
   * Submit a card review
   */
  async submitReview(
    userId: string,
    reviewData: CreateReviewData
  ): Promise<CardReview> {
    try {
      const { cardId, sessionId, quality, responseTimeMs } = reviewData;

      // Get current review data or create initial values
      let currentReview = await this.getCardReview(userId, cardId);

      let easeFactor = 2.5; // Default ease factor
      let intervalDays = 1;
      let repetitions = 0;

      if (currentReview) {
        easeFactor = currentReview.easeFactor;
        intervalDays = currentReview.intervalDays;
        repetitions = currentReview.repetitions;
      } else {
        // For new cards, calculate initial ease factor based on user performance
        const userStats = await this.getUserStats(userId);
        const card = await cardService.findById(cardId, userId);
        if (card) {
          easeFactor = SpacedRepetitionAlgorithm.calculateInitialEaseFactor(
            userStats.accuracyRate || 0.8,
            card.difficulty
          );
        }
      }

      // Calculate next review parameters
      const nextReview = SpacedRepetitionAlgorithm.calculateNextReview(
        quality,
        easeFactor,
        intervalDays,
        repetitions
      );

      // Insert or update review record
      const query = `
        INSERT INTO card_reviews (
          user_id, card_id, session_id, quality, ease_factor, interval_days, 
          repetitions, next_review_date, response_time_ms, previous_ease_factor, 
          previous_interval_days
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (user_id, card_id) 
        DO UPDATE SET
          session_id = EXCLUDED.session_id,
          quality = EXCLUDED.quality,
          ease_factor = EXCLUDED.ease_factor,
          interval_days = EXCLUDED.interval_days,
          repetitions = EXCLUDED.repetitions,
          next_review_date = EXCLUDED.next_review_date,
          response_time_ms = EXCLUDED.response_time_ms,
          reviewed_at = NOW(),
          previous_ease_factor = card_reviews.ease_factor,
          previous_interval_days = card_reviews.interval_days
        RETURNING id, user_id, card_id, session_id, quality, ease_factor, interval_days,
                  repetitions, next_review_date, response_time_ms, reviewed_at,
                  previous_ease_factor, previous_interval_days
      `;

      const values = [
        userId,
        cardId,
        sessionId || null,
        quality,
        nextReview.easeFactor,
        nextReview.intervalDays,
        nextReview.repetitions,
        nextReview.nextReviewDate,
        responseTimeMs || null,
        easeFactor, // previous ease factor
        intervalDays, // previous interval
      ];

      const result = await db.query(query, values);
      const review = dbHelpers.toCamelCase(result.rows[0]) as CardReview;

      logger.info("Card review submitted", {
        userId,
        cardId,
        quality,
        nextReviewDate: nextReview.nextReviewDate,
      });

      return review;
    } catch (error) {
      logger.error("Failed to submit card review", {
        error,
        userId,
        reviewData,
      });
      throw error;
    }
  }

  /**
   * Get card review data for a user
   */
  async getCardReview(
    userId: string,
    cardId: string
  ): Promise<CardReview | null> {
    try {
      const query = `
        SELECT id, user_id, card_id, session_id, quality, ease_factor, interval_days,
               repetitions, next_review_date, response_time_ms, reviewed_at,
               previous_ease_factor, previous_interval_days
        FROM card_reviews
        WHERE user_id = $1 AND card_id = $2
        ORDER BY reviewed_at DESC
        LIMIT 1
      `;

      const result = await db.query(query, [userId, cardId]);

      if (result.rows.length === 0) {
        return null;
      }

      return dbHelpers.toCamelCase(result.rows[0]) as CardReview;
    } catch (error) {
      logger.error("Failed to get card review", { error, userId, cardId });
      throw error;
    }
  }

  /**
   * Get user study statistics
   */
  async getUserStats(userId: string): Promise<StudyStats> {
    try {
      const query = `
        SELECT 
          COALESCE(COUNT(DISTINCT cr.card_id), 0) as total_cards_studied,
          COALESCE(AVG(CASE WHEN cr.quality >= 3 THEN 1.0 ELSE 0.0 END), 0) as accuracy_rate,
          COALESCE(AVG(ss.total_time_seconds), 0) as average_session_duration,
          COALESCE(SUM(ss.total_time_seconds), 0) as total_study_time,
          COALESCE(COUNT(CASE WHEN cr.repetitions >= 3 AND cr.ease_factor >= 2.5 THEN 1 END), 0) as cards_mastered,
          COALESCE(COUNT(CASE WHEN cr.repetitions > 0 AND cr.repetitions < 3 THEN 1 END), 0) as cards_learning,
          COALESCE(COUNT(CASE WHEN cr.repetitions = 0 THEN 1 END), 0) as cards_new
        FROM users u
        LEFT JOIN card_reviews cr ON u.id = cr.user_id
        LEFT JOIN study_sessions ss ON u.id = ss.user_id
        WHERE u.id = $1
        GROUP BY u.id
      `;

      const result = await db.query(query, [userId]);

      if (result.rows.length === 0) {
        // Return default stats for new users
        return {
          totalCardsStudied: 0,
          accuracyRate: 0,
          averageSessionDuration: 0,
          studyStreak: 0,
          totalStudyTime: 0,
          cardsMastered: 0,
          cardsLearning: 0,
          cardsNew: 0,
        };
      }

      const stats = dbHelpers.toCamelCase(result.rows[0]) as StudyStats;

      // Calculate study streak
      stats.studyStreak = await this.calculateStudyStreak(userId);

      return stats;
    } catch (error) {
      logger.error("Failed to get user stats", { error, userId });
      throw error;
    }
  }

  /**
   * Calculate user's current study streak
   */
  private async calculateStudyStreak(userId: string): Promise<number> {
    try {
      const query = `
        SELECT DATE(started_at) as study_date
        FROM study_sessions
        WHERE user_id = $1 AND ended_at IS NOT NULL
        GROUP BY DATE(started_at)
        ORDER BY study_date DESC
        LIMIT 365
      `;

      const result = await db.query(query, [userId]);

      if (result.rows.length === 0) {
        return 0;
      }

      let streak = 0;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < result.rows.length; i++) {
        const studyDate = new Date(result.rows[i].study_date);
        const expectedDate = new Date(today);
        expectedDate.setDate(today.getDate() - i);

        if (studyDate.getTime() === expectedDate.getTime()) {
          streak++;
        } else {
          break;
        }
      }

      return streak;
    } catch (error) {
      logger.error("Failed to calculate study streak", { error, userId });
      return 0;
    }
  }
}

export const studyService = new StudyService();
