import { db, dbHelpers } from "@/utils/database";
import {
  Card,
  CreateCardData,
  QueryOptions,
  AppError,
  PaginatedResponse,
} from "@/types";
import { deckService } from "./deckService";
import { logger } from "@/utils/logger";

export class CardService {
  /**
   * Create a new card
   */
  async create(
    deckId: string,
    userId: string,
    cardData: CreateCardData
  ): Promise<Card> {
    try {
      // Verify deck ownership
      const deck = await deckService.findById(deckId, userId);
      if (!deck) {
        throw new AppError("Deck not found", 404);
      }

      if (deck.userId !== userId) {
        throw new AppError("Not authorized to add cards to this deck", 403);
      }

      // Get next position if not provided
      let position = cardData.position;
      if (position === undefined) {
        const positionQuery = `
          SELECT COALESCE(MAX(position), -1) + 1 as next_position
          FROM cards
          WHERE deck_id = $1
        `;
        const positionResult = await db.query(positionQuery, [deckId]);
        position = positionResult.rows[0].next_position;
      }

      const query = `
        INSERT INTO cards (deck_id, front_content, back_content, card_type, difficulty, tags, position)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, deck_id, front_content, back_content, card_type, difficulty, tags, position,
                  created_at, updated_at, metadata
      `;

      const values = [
        deckId,
        JSON.stringify(cardData.frontContent),
        JSON.stringify(cardData.backContent),
        cardData.cardType || "basic",
        cardData.difficulty || 1,
        cardData.tags || [],
        position,
      ];

      const result = await db.query(query, values);
      const card = dbHelpers.toCamelCase(result.rows[0]) as Card;

      // JSONB fields are already parsed by PostgreSQL
      card.frontContent = result.rows[0].front_content;
      card.backContent = result.rows[0].back_content;

      logger.info("Card created", { cardId: card.id, deckId, userId });
      return card;
    } catch (error) {
      logger.error("Failed to create card", {
        error,
        deckId,
        userId,
        cardData,
      });
      throw error;
    }
  }

  /**
   * Find card by ID
   */
  async findById(id: string, userId?: string): Promise<Card | null> {
    try {
      let query = `
        SELECT c.id, c.deck_id, c.front_content, c.back_content, c.card_type, c.difficulty, 
               c.tags, c.position, c.created_at, c.updated_at, c.metadata,
               d.user_id, d.is_public
        FROM cards c
        JOIN decks d ON c.deck_id = d.id
        WHERE c.id = $1
      `;

      const values = [id];

      // If userId is provided, check ownership or public visibility
      if (userId) {
        query += ` AND (d.user_id = $2 OR d.is_public = true)`;
        values.push(userId);
      } else {
        query += ` AND d.is_public = true`;
      }

      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      const card = dbHelpers.toCamelCase(row) as Card;

      // JSONB fields are already parsed by PostgreSQL
      card.frontContent = row.front_content;
      card.backContent = row.back_content;

      // Remove deck info from card object
      delete (card as any).userId;
      delete (card as any).isPublic;

      return card;
    } catch (error) {
      logger.error("Failed to find card by ID", { error, id, userId });
      throw error;
    }
  }

  /**
   * Find cards by deck ID
   */
  async findByDeckId(
    deckId: string,
    userId?: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<Card>> {
    try {
      // Verify deck access
      const deck = await deckService.findById(deckId, userId);
      if (!deck) {
        throw new AppError("Deck not found", 404);
      }

      const {
        page = 1,
        limit = 50,
        sortBy = "position",
        sortOrder = "asc",
        search,
        filters = {},
      } = options;

      let whereClause = "WHERE deck_id = $1";
      const values = [deckId];
      let paramIndex = 2;

      // Add search functionality (search in content)
      if (search) {
        whereClause += ` AND (front_content::text ILIKE $${paramIndex} OR back_content::text ILIKE $${paramIndex})`;
        values.push(`%${search}%`);
        paramIndex++;
      }

      // Add filters
      if (filters.cardType) {
        whereClause += ` AND card_type = $${paramIndex}`;
        values.push(filters.cardType);
        paramIndex++;
      }

      if (filters.difficulty) {
        whereClause += ` AND difficulty = $${paramIndex}`;
        values.push(filters.difficulty);
        paramIndex++;
      }

      if (filters.tags && filters.tags.length > 0) {
        whereClause += ` AND tags && $${paramIndex}`;
        values.push(filters.tags);
        paramIndex++;
      }

      // Build order clause
      const orderClause = dbHelpers.buildOrderClause(sortBy, sortOrder);

      // Build pagination clause
      const { clause: paginationClause } = dbHelpers.buildPaginationClause(
        page,
        limit
      );

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM cards ${whereClause}`;
      const countResult = await db.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count);

      // Get cards
      const query = `
        SELECT id, deck_id, front_content, back_content, card_type, difficulty, tags, position,
               created_at, updated_at, metadata
        FROM cards
        ${whereClause}
        ${orderClause}
        ${paginationClause}
      `;

      const result = await db.query(query, values);
      const cards = result.rows.map((row: any) => {
        const card = dbHelpers.toCamelCase(row) as Card;
        // JSONB fields are already parsed by PostgreSQL
        card.frontContent = row.front_content;
        card.backContent = row.back_content;
        return card;
      });

      return {
        data: cards,
        pagination: {
          page,
          limit,
          total,
          totalPages: dbHelpers.calculateTotalPages(total, limit),
        },
      };
    } catch (error) {
      logger.error("Failed to find cards by deck ID", {
        error,
        deckId,
        userId,
        options,
      });
      throw error;
    }
  }

  /**
   * Update card
   */
  async update(
    id: string,
    userId: string,
    updates: Partial<Card>
  ): Promise<Card> {
    try {
      // Check ownership
      const existingCard = await this.findById(id, userId);
      if (!existingCard) {
        throw new AppError("Card not found", 404);
      }

      // Verify deck ownership
      const deck = await deckService.findById(existingCard.deckId, userId);
      if (!deck || deck.userId !== userId) {
        throw new AppError("Not authorized to update this card", 403);
      }

      const allowedFields = [
        "front_content",
        "back_content",
        "card_type",
        "difficulty",
        "tags",
        "position",
      ];
      const updateData = dbHelpers.toSnakeCase(updates);

      // Handle JSON fields
      if (updates.frontContent) {
        updateData.front_content = JSON.stringify(updates.frontContent);
      }
      if (updates.backContent) {
        updateData.back_content = JSON.stringify(updates.backContent);
      }

      // Filter only allowed fields
      const filteredUpdates = Object.keys(updateData)
        .filter((key) => allowedFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = updateData[key];
          return obj;
        }, {} as any);

      if (Object.keys(filteredUpdates).length === 0) {
        throw new AppError("No valid fields to update", 400);
      }

      const setClause = Object.keys(filteredUpdates)
        .map((key, index) => `${key} = $${index + 2}`)
        .join(", ");

      const query = `
        UPDATE cards
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING id, deck_id, front_content, back_content, card_type, difficulty, tags, position,
                  created_at, updated_at, metadata
      `;

      const values = [id, ...Object.values(filteredUpdates)];
      const result = await db.query(query, values);

      const card = dbHelpers.toCamelCase(result.rows[0]) as Card;

      // JSONB fields are already parsed by PostgreSQL
      card.frontContent = result.rows[0].front_content;
      card.backContent = result.rows[0].back_content;

      logger.info("Card updated", { cardId: id, userId });
      return card;
    } catch (error) {
      logger.error("Failed to update card", { error, id, userId, updates });
      throw error;
    }
  }

  /**
   * Delete card
   */
  async delete(id: string, userId: string): Promise<void> {
    try {
      // Check ownership
      const existingCard = await this.findById(id, userId);
      if (!existingCard) {
        throw new AppError("Card not found", 404);
      }

      // Verify deck ownership
      const deck = await deckService.findById(existingCard.deckId, userId);
      if (!deck || deck.userId !== userId) {
        throw new AppError("Not authorized to delete this card", 403);
      }

      const query = "DELETE FROM cards WHERE id = $1";
      const result = await db.query(query, [id]);

      if (result.rowCount === 0) {
        throw new AppError("Card not found", 404);
      }

      logger.info("Card deleted", { cardId: id, userId });
    } catch (error) {
      logger.error("Failed to delete card", { error, id, userId });
      throw error;
    }
  }

  /**
   * Bulk create cards
   */
  async bulkCreate(
    deckId: string,
    userId: string,
    cardsData: CreateCardData[]
  ): Promise<Card[]> {
    try {
      // Verify deck ownership
      const deck = await deckService.findById(deckId, userId);
      if (!deck) {
        throw new AppError("Deck not found", 404);
      }

      if (deck.userId !== userId) {
        throw new AppError("Not authorized to add cards to this deck", 403);
      }

      return await db.transaction(async (client) => {
        const cards: Card[] = [];

        // Get starting position
        const positionQuery = `
          SELECT COALESCE(MAX(position), -1) + 1 as next_position
          FROM cards
          WHERE deck_id = $1
        `;
        const positionResult = await client.query(positionQuery, [deckId]);
        let position = positionResult.rows[0].next_position;

        for (const cardData of cardsData) {
          const query = `
            INSERT INTO cards (deck_id, front_content, back_content, card_type, difficulty, tags, position)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id, deck_id, front_content, back_content, card_type, difficulty, tags, position,
                      created_at, updated_at, metadata
          `;

          const values = [
            deckId,
            JSON.stringify(cardData.frontContent),
            JSON.stringify(cardData.backContent),
            cardData.cardType || "basic",
            cardData.difficulty || 1,
            cardData.tags || [],
            cardData.position !== undefined ? cardData.position : position++,
          ];

          const result = await client.query(query, values);
          const card = dbHelpers.toCamelCase(result.rows[0]) as Card;

          // JSONB fields are already parsed by PostgreSQL
          card.frontContent = result.rows[0].front_content;
          card.backContent = result.rows[0].back_content;

          cards.push(card);
        }

        logger.info("Bulk cards created", {
          deckId,
          userId,
          count: cards.length,
        });
        return cards;
      });
    } catch (error) {
      logger.error("Failed to bulk create cards", {
        error,
        deckId,
        userId,
        count: cardsData.length,
      });
      throw error;
    }
  }

  /**
   * Reorder cards in deck
   */
  async reorder(
    deckId: string,
    userId: string,
    cardIds: string[]
  ): Promise<void> {
    try {
      // Verify deck ownership
      const deck = await deckService.findById(deckId, userId);
      if (!deck || deck.userId !== userId) {
        throw new AppError("Not authorized to reorder cards in this deck", 403);
      }

      await db.transaction(async (client) => {
        for (let i = 0; i < cardIds.length; i++) {
          const query = `
            UPDATE cards
            SET position = $1, updated_at = NOW()
            WHERE id = $2 AND deck_id = $3
          `;
          await client.query(query, [i, cardIds[i], deckId]);
        }
      });

      logger.info("Cards reordered", { deckId, userId, count: cardIds.length });
    } catch (error) {
      logger.error("Failed to reorder cards", {
        error,
        deckId,
        userId,
        cardIds,
      });
      throw error;
    }
  }
}

export const cardService = new CardService();
