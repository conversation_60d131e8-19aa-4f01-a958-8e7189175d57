import { Pool } from "pg";
import bcrypt from "bcryptjs";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl:
    process.env.NODE_ENV === "production"
      ? { rejectUnauthorized: false }
      : false,
});

async function seedDatabase() {
  const client = await pool.connect();

  try {
    console.log("🌱 Starting database seeding...");

    // Check if data already exists
    const userCheck = await client.query("SELECT COUNT(*) FROM users");
    const userCount = parseInt(userCheck.rows[0].count);

    if (userCount > 0) {
      console.log("ℹ️  Database already contains data. Skipping seed.");
      return;
    }

    // Create demo users
    console.log("👤 Creating demo users...");

    const demoPassword = await bcrypt.hash("demo123456", 12);

    // Set subscription expiration dates for paid tiers (1 year from now)
    const subscriptionExpiry = new Date();
    subscriptionExpiry.setFullYear(subscriptionExpiry.getFullYear() + 1);

    const users = [
      {
        email: "<EMAIL>",
        username: "demo_user",
        password_hash: demoPassword,
        first_name: "Demo",
        last_name: "User",
        subscription_tier: "pro",
        subscription_expires_at: subscriptionExpiry,
        email_verified: true,
      },
      {
        email: "<EMAIL>",
        username: "student",
        password_hash: demoPassword,
        first_name: "Student",
        last_name: "Learner",
        subscription_tier: "free",
        subscription_expires_at: null, // Free tier doesn't need expiration
        email_verified: true,
      },
      {
        email: "<EMAIL>",
        username: "teacher",
        password_hash: demoPassword,
        first_name: "Teacher",
        last_name: "Educator",
        subscription_tier: "premium",
        subscription_expires_at: subscriptionExpiry,
        email_verified: true,
      },
    ];

    const createdUsers = [];
    for (const user of users) {
      const result = await client.query(
        `
        INSERT INTO users (email, username, password_hash, first_name, last_name, subscription_tier, subscription_expires_at, email_verified)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, email, username
      `,
        [
          user.email,
          user.username,
          user.password_hash,
          user.first_name,
          user.last_name,
          user.subscription_tier,
          user.subscription_expires_at,
          user.email_verified,
        ]
      );

      createdUsers.push(result.rows[0]);
      console.log(`✅ Created user: ${result.rows[0].email}`);
    }

    // Create demo decks
    console.log("📚 Creating demo decks...");

    const decks = [
      {
        user_id: createdUsers[0].id, // demo user
        title: "Spanish Vocabulary - Basics",
        description: "Essential Spanish words for beginners",
        is_public: true,
        category: "Language Learning",
        tags: ["spanish", "vocabulary", "beginner"],
        difficulty_level: 1,
      },
      {
        user_id: createdUsers[0].id,
        title: "JavaScript Fundamentals",
        description: "Core JavaScript concepts and syntax",
        is_public: true,
        category: "Technology",
        tags: ["javascript", "programming", "web-development"],
        difficulty_level: 2,
      },
      {
        user_id: createdUsers[1].id, // student user
        title: "Biology - Cell Structure",
        description: "Parts of a cell and their functions",
        is_public: false,
        category: "Science",
        tags: ["biology", "cells", "anatomy"],
        difficulty_level: 3,
      },
      {
        user_id: createdUsers[2].id, // teacher user
        title: "World History Timeline",
        description: "Major historical events and dates",
        is_public: true,
        category: "History",
        tags: ["history", "timeline", "world-events"],
        difficulty_level: 2,
      },
    ];

    const createdDecks = [];
    for (const deck of decks) {
      const result = await client.query(
        `
        INSERT INTO decks (user_id, title, description, is_public, category, tags, difficulty_level)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, title
      `,
        [
          deck.user_id,
          deck.title,
          deck.description,
          deck.is_public,
          deck.category,
          deck.tags,
          deck.difficulty_level,
        ]
      );

      createdDecks.push({ ...result.rows[0], user_id: deck.user_id });
      console.log(`✅ Created deck: ${result.rows[0].title}`);
    }

    // Create demo cards
    console.log("🃏 Creating demo cards...");

    const cardSets = [
      // Spanish Vocabulary cards
      {
        deck_id: createdDecks[0].id,
        cards: [
          {
            front: { type: "text", content: "Hello" },
            back: { type: "text", content: "Hola" },
            tags: ["greetings"],
          },
          {
            front: { type: "text", content: "Goodbye" },
            back: { type: "text", content: "Adiós" },
            tags: ["greetings"],
          },
          {
            front: { type: "text", content: "Thank you" },
            back: { type: "text", content: "Gracias" },
            tags: ["politeness"],
          },
          {
            front: { type: "text", content: "Please" },
            back: { type: "text", content: "Por favor" },
            tags: ["politeness"],
          },
          {
            front: { type: "text", content: "Water" },
            back: { type: "text", content: "Agua" },
            tags: ["nouns", "drinks"],
          },
        ],
      },
      // JavaScript cards
      {
        deck_id: createdDecks[1].id,
        cards: [
          {
            front: {
              type: "text",
              content: 'What does "var" do in JavaScript?',
            },
            back: {
              type: "text",
              content:
                "Declares a variable with function scope or global scope",
            },
            tags: ["variables", "syntax"],
          },
          {
            front: {
              type: "text",
              content: "What is the difference between == and ===?",
            },
            back: {
              type: "text",
              content:
                "== compares values with type coercion, === compares values and types strictly",
            },
            tags: ["operators", "comparison"],
          },
          {
            front: {
              type: "text",
              content: "What is a closure in JavaScript?",
            },
            back: {
              type: "text",
              content:
                "A function that has access to variables in its outer (enclosing) scope even after the outer function has returned",
            },
            tags: ["functions", "scope"],
          },
        ],
      },
      // Biology cards
      {
        deck_id: createdDecks[2].id,
        cards: [
          {
            front: {
              type: "text",
              content: "What is the function of the nucleus?",
            },
            back: {
              type: "text",
              content: "Controls cell activities and contains the cell's DNA",
            },
            tags: ["organelles", "nucleus"],
          },
          {
            front: { type: "text", content: "What are mitochondria?" },
            back: {
              type: "text",
              content: "The powerhouses of the cell that produce ATP energy",
            },
            tags: ["organelles", "energy"],
          },
        ],
      },
      // History cards
      {
        deck_id: createdDecks[3].id,
        cards: [
          {
            front: { type: "text", content: "When did World War II end?" },
            back: { type: "text", content: "September 2, 1945" },
            tags: ["wwii", "dates"],
          },
          {
            front: {
              type: "text",
              content: "Who was the first person to walk on the moon?",
            },
            back: { type: "text", content: "Neil Armstrong (July 20, 1969)" },
            tags: ["space", "achievements"],
          },
        ],
      },
    ];

    for (const cardSet of cardSets) {
      let position = 0;
      for (const cardData of cardSet.cards) {
        await client.query(
          `
          INSERT INTO cards (deck_id, front_content, back_content, tags, position)
          VALUES ($1, $2, $3, $4, $5)
        `,
          [
            cardSet.deck_id,
            JSON.stringify(cardData.front),
            JSON.stringify(cardData.back),
            cardData.tags,
            position++,
          ]
        );
      }
      console.log(
        `✅ Created ${cardSet.cards.length} cards for deck ${cardSet.deck_id}`
      );
    }

    // Create some sample study sessions and reviews
    console.log("📊 Creating sample study data...");

    // Create a study session for the demo user
    const sessionResult = await client.query(
      `
      INSERT INTO study_sessions (user_id, deck_id, session_type, started_at, ended_at, cards_studied, correct_answers, total_time_seconds)
      VALUES ($1, $2, $3, NOW() - INTERVAL '1 hour', NOW() - INTERVAL '45 minutes', 5, 4, 900)
      RETURNING id
    `,
      [createdUsers[0].id, createdDecks[0].id, "review"]
    );

    const sessionId = sessionResult.rows[0].id;

    // Create some card reviews
    const cardQuery = await client.query(
      "SELECT id FROM cards WHERE deck_id = $1 LIMIT 3",
      [createdDecks[0].id]
    );
    const cards = cardQuery.rows;

    for (let i = 0; i < cards.length; i++) {
      const quality = 3 + Math.floor(Math.random() * 3); // Quality 3-5
      const nextReviewDate = new Date();
      nextReviewDate.setDate(nextReviewDate.getDate() + (quality - 2)); // 1-3 days from now

      await client.query(
        `
        INSERT INTO card_reviews (user_id, card_id, session_id, quality, ease_factor, interval_days, repetitions, next_review_date, response_time_ms)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `,
        [
          createdUsers[0].id,
          cards[i].id,
          sessionId,
          quality,
          2.5,
          quality - 2,
          1,
          nextReviewDate,
          2000 + Math.floor(Math.random() * 3000), // 2-5 seconds
        ]
      );
    }

    console.log("✅ Created sample study sessions and reviews");

    console.log("🎉 Database seeding completed successfully!");
    console.log("\n📋 Demo Accounts Created:");
    console.log("Email: <EMAIL> | Password: demo123456 | Tier: Pro");
    console.log(
      "Email: <EMAIL> | Password: demo123456 | Tier: Free"
    );
    console.log(
      "Email: <EMAIL> | Password: demo123456 | Tier: Premium"
    );
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await seedDatabase();
    console.log("🏁 Seeding process completed");
    process.exit(0);
  } catch (error) {
    console.error("💥 Seeding process failed:", error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  main();
}

export { seedDatabase };
