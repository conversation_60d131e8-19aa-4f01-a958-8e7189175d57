const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moflash_dev',
  user: 'moflash_user',
  password: 'moflash_password',
});

async function fixSubscriptions() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Fixing subscription expiration dates...');
    
    // Set expiration date to 1 year from now for paid tiers
    const result = await client.query(`
      UPDATE users 
      SET subscription_expires_at = NOW() + INTERVAL '1 year' 
      WHERE subscription_tier IN ('pro', 'premium')
      RETURNING email, subscription_tier, subscription_expires_at
    `);
    
    console.log('✅ Updated subscriptions:');
    result.rows.forEach(user => {
      console.log(`  - ${user.email}: ${user.subscription_tier} (expires: ${user.subscription_expires_at})`);
    });
    
    console.log('🎉 Subscription fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing subscriptions:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixSubscriptions();
